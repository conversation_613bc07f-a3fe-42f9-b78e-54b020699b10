'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { TrendingStock } from '@/types/stock';
import { TrendingUp, TrendingDown, Minus, Activity } from 'lucide-react';

interface TrendingStocksProps {
  stocks: TrendingStock[];
  isLoading?: boolean;
}

const TrendingStocks: React.FC<TrendingStocksProps> = ({ stocks, isLoading = false }) => {
  const router = useRouter();

  const handleStockClick = (ticker: string) => {
    router.push(`/stock/${ticker}`);
  };

  const getPriceChangeColor = (change: number): string => {
    if (change > 0) return 'text-green-600 dark:text-green-400';
    if (change < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />;
    if (change < 0) return <TrendingDown className="h-4 w-4" />;
    return <Minus className="h-4 w-4" />;
  };

  const getBadgeVariant = (change: number) => {
    if (change > 0) return 'default';
    if (change < 0) return 'destructive';
    return 'secondary';
  };

  const formatPrice = (price: string): string => {
    const numPrice = parseFloat(price);
    return `₹${numPrice.toLocaleString()}`;
  };

  const formatPercentage = (percentage: number): string => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(2)}%`;
  };

  const formatPoints = (points: number): string => {
    const sign = points >= 0 ? '+' : '';
    return `${sign}${points.toFixed(2)}`;
  };

  if (isLoading) {
    return (
      <div className="bg-gradient-to-r from-primary/10 via-orange-500/10 to-primary/10 border-y border-primary/20 py-3">
        <div className="flex items-center gap-4 px-4">
          <div className="flex items-center gap-2 text-primary font-semibold">
            <Activity className="h-5 w-5 animate-pulse" />
            <span>TRENDING STOCKS</span>
          </div>
          <div className="flex gap-6 animate-pulse">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <div className="h-4 bg-gray-300 rounded w-16"></div>
                <div className="h-3 bg-gray-200 rounded w-12"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (stocks.length === 0) {
    return (
      <div className="bg-gradient-to-r from-primary/10 via-orange-500/10 to-primary/10 border-y border-primary/20 py-3">
        <div className="flex items-center gap-4 px-4">
          <div className="flex items-center gap-2 text-primary font-semibold">
            <Activity className="h-5 w-5" />
            <span>TRENDING STOCKS</span>
          </div>
          <span className="text-muted-foreground">No trending data available</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-r from-primary/10 via-orange-500/10 to-primary/10 border-y border-primary/20 py-3 overflow-hidden shadow-sm">
      <div className="flex items-center gap-4">
        {/* Fixed Label */}
        <div className="flex items-center gap-2 text-primary font-bold px-6 py-1 bg-background/90 backdrop-blur-sm rounded-r-full border-r border-primary/30 flex-shrink-0 shadow-sm">
          <Activity className="h-5 w-5 animate-pulse" />
          <span className="whitespace-nowrap text-sm tracking-wide">TRENDING STOCKS</span>
        </div>

        {/* Scrolling Content */}
        <div className="flex-1 overflow-hidden">
          <div className="flex animate-marquee hover:animation-paused gap-8 whitespace-nowrap">
            {/* First set of stocks */}
            {stocks.map((stock) => (
              <div
                key={`first-${stock.ticker}`}
                className="flex items-center gap-4 flex-shrink-0 hover:bg-background/50 px-3 py-1 rounded-lg transition-colors cursor-pointer"
                onClick={() => handleStockClick(stock.ticker)}
              >
                <div className="flex items-center gap-3">
                  <span className="font-bold text-foreground text-sm bg-primary/10 px-2 py-1 rounded">{stock.ticker}</span>
                  <span className="font-semibold text-base financial-number">{formatPrice(stock.latest_price)}</span>
                  <div className={`flex items-center gap-1 ${getPriceChangeColor(stock.percentage_change)} font-medium text-sm`}>
                    {getPriceChangeIcon(stock.percentage_change)}
                    <span>{formatPercentage(stock.percentage_change)}</span>
                  </div>
                </div>
                <div className="w-px h-6 bg-border/50"></div>
              </div>
            ))}

            {/* Duplicate set for seamless loop */}
            {stocks.map((stock) => (
              <div
                key={`second-${stock.ticker}`}
                className="flex items-center gap-4 flex-shrink-0 hover:bg-background/50 px-3 py-1 rounded-lg transition-colors cursor-pointer"
                onClick={() => handleStockClick(stock.ticker)}
              >
                <div className="flex items-center gap-3">
                  <span className="font-bold text-foreground text-sm bg-primary/10 px-2 py-1 rounded">{stock.ticker}</span>
                  <span className="font-semibold text-base financial-number">{formatPrice(stock.latest_price)}</span>
                  <div className={`flex items-center gap-1 ${getPriceChangeColor(stock.percentage_change)} font-medium text-sm`}>
                    {getPriceChangeIcon(stock.percentage_change)}
                    <span>{formatPercentage(stock.percentage_change)}</span>
                  </div>
                </div>
                <div className="w-px h-6 bg-border/50"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrendingStocks;
