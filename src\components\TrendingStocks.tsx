'use client';

import React from 'react';
import { TrendingStock } from '@/types/stock';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus, Activity } from 'lucide-react';

interface TrendingStocksProps {
  stocks: TrendingStock[];
  isLoading?: boolean;
}

const TrendingStocks: React.FC<TrendingStocksProps> = ({ stocks, isLoading = false }) => {
  const getPriceChangeColor = (change: number): string => {
    if (change > 0) return 'text-green-600 dark:text-green-400';
    if (change < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />;
    if (change < 0) return <TrendingDown className="h-4 w-4" />;
    return <Minus className="h-4 w-4" />;
  };

  const getBadgeVariant = (change: number) => {
    if (change > 0) return 'default';
    if (change < 0) return 'destructive';
    return 'secondary';
  };

  const formatPrice = (price: string): string => {
    const numPrice = parseFloat(price);
    return new Intl.NumberFormat('en-NP', {
      style: 'currency',
      currency: 'NPR',
      minimumFractionDigits: 2,
    }).format(numPrice);
  };

  const formatPercentage = (percentage: number): string => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(2)}%`;
  };

  const formatPoints = (points: number): string => {
    const sign = points >= 0 ? '+' : '';
    return `${sign}${points.toFixed(2)}`;
  };

  if (isLoading) {
    return (
      <Card className="market-card">
        <CardHeader className="trading-header">
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent flex items-center gap-3">
            <Activity className="h-6 w-6 text-primary animate-pulse" />
            Trending Stocks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-32"></div>
                  <div className="h-3 bg-gray-200 rounded w-24"></div>
                </div>
                <div className="text-right space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-20"></div>
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (stocks.length === 0) {
    return (
      <Card className="market-card">
        <CardHeader className="trading-header">
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent flex items-center gap-3">
            <Activity className="h-6 w-6 text-primary" />
            Trending Stocks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No trending stocks data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="market-card">
      <CardHeader className="trading-header">
        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent flex items-center gap-3">
          <Activity className="h-6 w-6 text-primary" />
          Trending Stocks
        </CardTitle>
        <p className="text-muted-foreground">
          Most active stocks by market cap trading • {stocks.length} stocks
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {stocks.map((stock, index) => (
            <div
              key={stock.ticker}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-primary/5 transition-all duration-200 cursor-pointer"
            >
              <div className="flex items-center gap-4">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold text-sm">
                  {index + 1}
                </div>
                <div>
                  <div className="font-semibold text-foreground">{stock.ticker}</div>
                  <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                    {stock.ticker_name}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Market Cap: {(stock.traded_of_mkt_cap * 100).toFixed(2)}%
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-bold text-lg">
                  {formatPrice(stock.latest_price)}
                </div>
                <div className="flex items-center gap-2 justify-end">
                  <Badge 
                    variant={getBadgeVariant(stock.percentage_change)}
                    className="flex items-center gap-1"
                  >
                    {getPriceChangeIcon(stock.percentage_change)}
                    {formatPercentage(stock.percentage_change)}
                  </Badge>
                </div>
                <div className={`text-sm ${getPriceChangeColor(stock.points_change)}`}>
                  {formatPoints(stock.points_change)} pts
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default TrendingStocks;
