import { LiveStock, TrendingStock } from '@/types/stock';

const API_URL = 'https://www.onlinekhabar.com/smtm/stock_live/live-trading';
const TRENDING_API_URL = 'https://www.onlinekhabar.com/smtm/home/<USER>';

export async function fetchLiveStocks(): Promise<LiveStock[]> {
  console.log('🚀 Starting fetchLiveStocks...');
  try {
    // Try direct fetch first
    let response;
    try {
      console.log('📡 Attempting to fetch from:', API_URL);
      response = await fetch(API_URL, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        // Add cache control for live data
        cache: 'no-store',
        mode: 'cors',
      });
      console.log('✅ Fetch successful, status:', response.status);
    } catch (corsError) {
      // If CORS fails, try using a proxy or return mock data for development
      console.warn('❌ Network error (possibly CORS), using fallback data:', corsError);
      return getMockData();
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('API Response received:', {
      isArray: Array.isArray(data),
      hasResponse: !!data.response,
      hasData: !!data.data,
      responseLength: data.response?.length,
      dataLength: data.data?.length,
      keys: Object.keys(data)
    });

    // The API might return an array directly or wrapped in an object
    // Adjust based on actual API response structure
    if (Array.isArray(data)) {
      console.log('Using direct array, length:', data.length);
      return data;
    } else if (data.response && Array.isArray(data.response)) {
      console.log('Using data.response, length:', data.response.length);
      return data.response;
    } else if (data.data && Array.isArray(data.data)) {
      console.log('Using data.data, length:', data.data.length);
      return data.data;
    } else {
      console.error('Unexpected API response format:', data);
      throw new Error('Unexpected API response format');
    }
  } catch (error) {
    console.error('💥 Error fetching live stock data:', error);
    console.log('🔄 Falling back to mock data...');
    // Return mock data for development/demo purposes
    return getMockData();
  }
}

export async function fetchTrendingStocks(): Promise<TrendingStock[]> {
  console.log('📈 Starting fetchTrendingStocks...');
  try {
    // Try direct fetch first
    let response;
    try {
      console.log('📡 Attempting to fetch trending from:', TRENDING_API_URL);
      response = await fetch(TRENDING_API_URL, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        // Add cache control for live data
        cache: 'no-store',
        mode: 'cors',
      });
      console.log('✅ Trending fetch successful, status:', response.status);
    } catch (corsError) {
      // If CORS fails, try using a proxy or return mock data for development
      console.warn('❌ Trending network error (possibly CORS), using fallback data:', corsError);
      return getTrendingMockData();
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Trending API Response received:', {
      isArray: Array.isArray(data),
      hasResponse: !!data.response,
      hasData: !!data.data,
      responseLength: data.response?.length,
      dataLength: data.data?.length,
      keys: Object.keys(data)
    });

    // The API might return an array directly or wrapped in an object
    // Adjust based on actual API response structure
    if (Array.isArray(data)) {
      console.log('Using direct array, length:', data.length);
      return data;
    } else if (data.response && Array.isArray(data.response)) {
      console.log('Using data.response, length:', data.response.length);
      return data.response;
    } else if (data.data && Array.isArray(data.data)) {
      console.log('Using data.data, length:', data.data.length);
      return data.data;
    } else {
      console.error('Unexpected trending API response format:', data);
      throw new Error('Unexpected API response format');
    }
  } catch (error) {
    console.error('💥 Error fetching trending stock data:', error);
    console.log('🔄 Falling back to trending mock data...');
    // Return mock data for development/demo purposes
    return getTrendingMockData();
  }
}

// Test function to manually check API (for debugging)
export async function testAPI() {
  try {
    console.log('🧪 Testing live stocks API directly...');
    const response = await fetch(API_URL);
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    const data = await response.json();
    console.log('Response data structure:', {
      isArray: Array.isArray(data),
      keys: Object.keys(data),
      hasResponse: !!data.response,
      responseLength: data.response?.length,
      firstItem: data.response?.[0]
    });
    return data;
  } catch (error) {
    console.error('Test API error:', error);
    return null;
  }
}

// Test function to manually check trending API (for debugging)
export async function testTrendingAPI() {
  try {
    console.log('🧪 Testing trending stocks API directly...');
    const response = await fetch(TRENDING_API_URL);
    console.log('Trending response status:', response.status);
    console.log('Trending response headers:', Object.fromEntries(response.headers.entries()));
    const data = await response.json();
    console.log('Trending response data structure:', {
      isArray: Array.isArray(data),
      keys: Object.keys(data),
      hasResponse: !!data.response,
      responseLength: data.response?.length,
      firstItem: data.response?.[0]
    });
    return data;
  } catch (error) {
    console.error('Test trending API error:', error);
    return null;
  }
}

// Mock data for development/demo purposes
function getMockData(): LiveStock[] {
  return [
    {
      ticker: "SONA",
      indices: "Manu.& Pro.",
      ticker_name: "Sonapur Minerals And Oil Limited",
      ltp: 514,
      ltv: 10,
      point_change: 16.96,
      percentage_change: 3.41,
      open: 495.1,
      high: 517.9,
      low: 494,
      volume: 101013,
      previousClosing: 497.04,
      calculated_on: "2025-07-27 12:11:07",
      amount: ********.7,
      datasource: "Live",
      icon: "images.smtmcapital.com.np/logos/SONA.png"
    },
    {
      ticker: "NABIL",
      indices: "Commercial Banks",
      ticker_name: "Nabil Bank Limited",
      ltp: 1250,
      ltv: 25,
      point_change: -15.50,
      percentage_change: -1.22,
      open: 1265.5,
      high: 1270,
      low: 1245,
      volume: 85420,
      previousClosing: 1265.5,
      calculated_on: "2025-07-27 12:11:07",
      amount: *********,
      datasource: "Live",
      icon: "images.smtmcapital.com.np/logos/NABIL.png"
    },
    {
      ticker: "NIMB",
      indices: "Commercial Banks",
      ticker_name: "Nepal Investment Mega Bank Limited",
      ltp: 385,
      ltv: 15,
      point_change: 8.25,
      percentage_change: 2.19,
      open: 376.75,
      high: 390,
      low: 375,
      volume: 125630,
      previousClosing: 376.75,
      calculated_on: "2025-07-27 12:11:07",
      amount: ********,
      datasource: "Live",
      icon: "images.smtmcapital.com.np/logos/NIMB.png"
    },
    {
      ticker: "SCB",
      indices: "Commercial Banks",
      ticker_name: "Standard Chartered Bank Nepal Limited",
      ltp: 420,
      ltv: 8,
      point_change: 0,
      percentage_change: 0,
      open: 420,
      high: 425,
      low: 415,
      volume: 45200,
      previousClosing: 420,
      calculated_on: "2025-07-27 12:11:07",
      amount: ********,
      datasource: "Live",
      icon: "images.smtmcapital.com.np/logos/SCB.png"
    },
    {
      ticker: "HBL",
      indices: "Commercial Banks",
      ticker_name: "Himalayan Bank Limited",
      ltp: 580,
      ltv: 20,
      point_change: 12.30,
      percentage_change: 2.17,
      open: 567.7,
      high: 585,
      low: 565,
      volume: 95840,
      previousClosing: 567.7,
      calculated_on: "2025-07-27 12:11:07",
      amount: ********,
      datasource: "Live",
      icon: "images.smtmcapital.com.np/logos/HBL.png"
    }
  ];
}

// Mock trending data for development/demo purposes
function getTrendingMockData(): TrendingStock[] {
  return [
    {
      ticker: "RADHI",
      ticker_name: "Radhi Bidhyut Company",
      latest_price: "876",
      points_change: 5.46,
      percentage_change: 0.63,
      traded_of_mkt_cap: 0.0482
    },
    {
      ticker: "NGPL",
      ticker_name: "Nepal Gas Pipeline Limited",
      latest_price: "542",
      points_change: 12.30,
      percentage_change: 2.32,
      traded_of_mkt_cap: 0.0356
    },
    {
      ticker: "NABIL",
      ticker_name: "Nabil Bank Limited",
      latest_price: "1250",
      points_change: -8.50,
      percentage_change: -0.67,
      traded_of_mkt_cap: 0.0789
    },
    {
      ticker: "NICA",
      ticker_name: "NIC Asia Bank Limited",
      latest_price: "890",
      points_change: 15.20,
      percentage_change: 1.74,
      traded_of_mkt_cap: 0.0623
    },
    {
      ticker: "SANIMA",
      ticker_name: "Sanima Bank Limited",
      latest_price: "320",
      points_change: -2.10,
      percentage_change: -0.65,
      traded_of_mkt_cap: 0.0445
    }
  ];
}

// Helper function to format currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-NP', {
    style: 'currency',
    currency: 'NPR',
    minimumFractionDigits: 2,
  }).format(amount);
}

// Helper function to format volume
export function formatVolume(volume: number): string {
  if (volume >= 1000000) {
    return `${(volume / 1000000).toFixed(1)}M`;
  } else if (volume >= 1000) {
    return `${(volume / 1000).toFixed(1)}K`;
  }
  return volume.toString();
}

// Helper function to format percentage
export function formatPercentage(percentage: number): string {
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(2)}%`;
}

// Helper function to get price change color class
export function getPriceChangeColor(change: number): string {
  if (change > 0) return 'text-green-600 dark:text-green-400';
  if (change < 0) return 'text-red-600 dark:text-red-400';
  return 'text-gray-600 dark:text-gray-400';
}

// Helper function to get price change icon
export function getPriceChangeIcon(change: number): 'up' | 'down' | 'neutral' {
  if (change > 0) return 'up';
  if (change < 0) return 'down';
  return 'neutral';
}
