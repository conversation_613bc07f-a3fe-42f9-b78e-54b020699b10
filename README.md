# Live Stock Market Trading Dashboard

A real-time stock market trading dashboard built with Next.js, TypeScript, and shadcn/ui components. This application fetches live trading data from Nepal's stock market and provides an interactive interface to monitor, filter, and analyze real-time stock performance.

## Features

### 📈 Live Trading Data
- **Real-time Updates**: Fetches live trading data from Nepal's stock market API
- **Auto-refresh**: Automatically updates data every 30 seconds
- **Live Prices**: Current Last Traded Price (LTP) with real-time changes
- **Price Movements**: Point changes and percentage changes with color coding
- **Trading Volume**: Live volume and trading amount data
- **Market Status**: Real-time market data source indicators

### 📊 Advanced Analytics
- **Market Sentiment**: Live count of gainers, losers, and unchanged stocks
- **Top Performers**: Real-time top gainers and losers with percentage changes
- **Volume Analysis**: Total trading volume and amount across all stocks
- **Sector Performance**: Live sector-wise stock distribution and performance

### 🔍 Advanced Filtering & Search
- **Real-time Search**: Search across company names and ticker symbols
- **Sector Filter**: Filter by business sectors (Banking, Manufacturing, etc.)
- **Data Source Filter**: Filter by live vs. other data sources
- **Performance Filter**: Sort by price, change percentage, volume
- **Clear Filters**: One-click reset of all filters

### 📋 Professional Trading Interface
- **Sortable Columns**: Sort by company name, ticker, LTP, change %, volume, sector
- **Price Change Indicators**: Visual up/down arrows with color coding
- **Company Logos**: Display company logos when available
- **Pagination**: Navigate through large datasets (20 items per page)
- **Responsive Design**: Optimized for desktop and mobile trading

### 🎨 Professional Trading UI/UX
- **Financial Color Scheme**: Professional green/red/blue color palette inspired by trading platforms
- **Bull & Bear Market Colors**: Green for gains, red for losses, blue for neutral states
- **Lucide React Icons**: Professional, consistent icons throughout the interface
- **Trading Typography**: Tabular numbers and monospace fonts for precise data display
- **shadcn/ui Components**: Beautiful, accessible components with financial styling
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Dark/Light Mode Toggle**: Manual theme switching optimized for trading environments
- **Live Data Indicators**: Visual feedback for loading states and data freshness

## Technology Stack

- **Framework**: Next.js 15.4.4 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4 with custom trading platform theme
- **UI Components**: shadcn/ui with financial styling
- **Icons**: Lucide React for professional, consistent iconography
- **API Integration**: Live data fetching from Nepal Stock Market API
- **Color System**: Custom OKLCH color palette optimized for trading
- **Typography**: Tabular numbers and monospace fonts for financial data
- **State Management**: React hooks for real-time data management

## Data Structure

The application works with stock data containing:
- Company information (name, symbol, security name)
- Business details (sector, instrument type, regulatory body)
- Status information (active, delisted, suspended)
- Contact details (email, website)

## Getting Started

1. **Install dependencies**:
```bash
npm install
```

2. **Run the development server**:
```bash
npm run dev
```

3. **Open your browser**:
Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/
│   ├── page.tsx          # Main page component
│   ├── layout.tsx        # Root layout
│   └── globals.css       # Global styles
├── components/
│   ├── ui/               # shadcn/ui components
│   ├── StockListing.tsx  # Main data table component
│   ├── StockDetailModal.tsx # Detailed view modal
│   └── StockStatistics.tsx  # Statistics dashboard
├── types/
│   └── stock.ts          # TypeScript type definitions
└── stocks.json           # Stock data file
```

## Usage Examples

### Filtering Data
1. Use the search box to find specific companies
2. Select a sector from the dropdown to view companies in that sector
3. Filter by instrument type (typically "Equity")
4. Filter by status to see only active, delisted, or suspended companies

### Viewing Details
1. Click on any company row in the table
2. A modal will open with complete company information
3. Use the action buttons to visit the company website or send an email

### Sorting Data
1. Click on any column header to sort by that field
2. Click again to reverse the sort order
3. Sort indicators (↑↓) show the current sort direction

## Stock Market Color Theme

The application uses a professional financial color scheme:

### Light Mode
- **Background**: Subtle green-tinted white for a fresh, professional look
- **Primary**: Deep green (#22c55e) representing bull markets and gains
- **Success**: Bright green for active stocks and positive indicators
- **Destructive**: Financial red for delisted stocks and losses
- **Cards**: Pure white with subtle shadows and green-tinted borders

### Dark Mode
- **Background**: Dark navy blue for reduced eye strain during long trading sessions
- **Primary**: Bright green that stands out against dark backgrounds
- **Success**: Enhanced green for better visibility in dark environments
- **Destructive**: Bright red for clear loss indicators
- **Cards**: Dark blue-gray with green accent borders

### Status Colors
- 🟢 **Active (A)**: Green - healthy, trading stocks
- 🔴 **Delisted (D)**: Red - removed from trading
- 🟡 **Suspended (S)**: Yellow - temporarily halted

## Customization

The application is highly customizable:
- Modify `src/stocks.json` to use your own data
- Update the `Stock` interface in `src/types/stock.ts` for different data structures
- Customize the color theme in `src/app/globals.css`
- Add new filter options by extending the filter components
- Modify financial styling classes for different visual preferences

## Performance Features

- **Client-side filtering**: Fast, responsive filtering without server requests
- **Pagination**: Efficient rendering of large datasets
- **Memoized calculations**: Optimized performance for statistics and filtering
- **Lazy loading**: Components load only when needed

## Accessibility

- **Keyboard navigation**: Full keyboard support for all interactive elements
- **Screen reader friendly**: Proper ARIA labels and semantic HTML
- **Color contrast**: High contrast colors for better readability
- **Focus indicators**: Clear focus states for keyboard users
