@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Stock Market Theme - Light Mode */
  --background: oklch(0.99 0.005 120); /* Very light green-tinted white */
  --foreground: oklch(0.15 0.02 240); /* Dark navy blue */
  --card: oklch(1 0 0); /* Pure white for cards */
  --card-foreground: oklch(0.15 0.02 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 240);
  --primary: oklch(0.45 0.15 150); /* Professional green */
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.96 0.01 120); /* Light green tint */
  --secondary-foreground: oklch(0.2 0.02 240);
  --muted: oklch(0.95 0.01 120);
  --muted-foreground: oklch(0.5 0.02 240);
  --accent: oklch(0.92 0.02 60); /* Light gold accent */
  --accent-foreground: oklch(0.2 0.02 240);
  --destructive: oklch(0.55 0.22 15); /* Stock red */
  --border: oklch(0.9 0.01 120);
  --input: oklch(0.98 0.005 120);
  --ring: oklch(0.45 0.15 150);
  /* Stock-specific colors */
  --success: oklch(0.5 0.18 145); /* Bull market green */
  --warning: oklch(0.65 0.15 60); /* Caution yellow */
  --info: oklch(0.55 0.12 220); /* Information blue */
  --chart-1: oklch(0.5 0.18 145); /* Green for gains */
  --chart-2: oklch(0.55 0.22 15); /* Red for losses */
  --chart-3: oklch(0.55 0.12 220); /* Blue for neutral */
  --chart-4: oklch(0.65 0.15 60); /* Yellow for warning */
  --chart-5: oklch(0.45 0.1 280); /* Purple for special */
  --sidebar: oklch(0.98 0.005 120);
  --sidebar-foreground: oklch(0.15 0.02 240);
  --sidebar-primary: oklch(0.45 0.15 150);
  --sidebar-primary-foreground: oklch(0.98 0 0);
  --sidebar-accent: oklch(0.96 0.01 120);
  --sidebar-accent-foreground: oklch(0.2 0.02 240);
  --sidebar-border: oklch(0.9 0.01 120);
  --sidebar-ring: oklch(0.45 0.15 150);
}

.dark {
  /* Stock Market Theme - Dark Mode */
  --background: oklch(0.08 0.01 240); /* Dark navy background */
  --foreground: oklch(0.95 0.01 120); /* Light green-tinted text */
  --card: oklch(0.12 0.01 240); /* Slightly lighter dark cards */
  --card-foreground: oklch(0.95 0.01 120);
  --popover: oklch(0.12 0.01 240);
  --popover-foreground: oklch(0.95 0.01 120);
  --primary: oklch(0.6 0.18 145); /* Bright green for dark mode */
  --primary-foreground: oklch(0.08 0.01 240);
  --secondary: oklch(0.18 0.01 240); /* Dark blue-gray */
  --secondary-foreground: oklch(0.9 0.01 120);
  --muted: oklch(0.15 0.01 240);
  --muted-foreground: oklch(0.65 0.01 120);
  --accent: oklch(0.25 0.02 60); /* Dark gold accent */
  --accent-foreground: oklch(0.9 0.01 120);
  --destructive: oklch(0.65 0.25 15); /* Bright red for dark mode */
  --border: oklch(0.2 0.01 240);
  --input: oklch(0.15 0.01 240);
  --ring: oklch(0.6 0.18 145);
  /* Stock-specific colors for dark mode */
  --success: oklch(0.6 0.18 145); /* Bright bull green */
  --warning: oklch(0.7 0.15 60); /* Bright caution yellow */
  --info: oklch(0.65 0.12 220); /* Bright info blue */
  --chart-1: oklch(0.6 0.18 145); /* Green for gains */
  --chart-2: oklch(0.65 0.25 15); /* Red for losses */
  --chart-3: oklch(0.65 0.12 220); /* Blue for neutral */
  --chart-4: oklch(0.7 0.15 60); /* Yellow for warning */
  --chart-5: oklch(0.6 0.15 280); /* Purple for special */
  --sidebar: oklch(0.12 0.01 240);
  --sidebar-foreground: oklch(0.95 0.01 120);
  --sidebar-primary: oklch(0.6 0.18 145);
  --sidebar-primary-foreground: oklch(0.08 0.01 240);
  --sidebar-accent: oklch(0.18 0.01 240);
  --sidebar-accent-foreground: oklch(0.9 0.01 120);
  --sidebar-border: oklch(0.2 0.01 240);
  --sidebar-ring: oklch(0.6 0.18 145);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "tnum"; /* Tabular numbers for financial data */
  }
}

@layer components {
  /* Stock Market Specific Components */
  .stock-gain {
    @apply text-green-600 dark:text-green-400;
  }

  .stock-loss {
    @apply text-red-600 dark:text-red-400;
  }

  .stock-neutral {
    @apply text-blue-600 dark:text-blue-400;
  }

  .stock-symbol {
    @apply font-mono font-bold tracking-wider;
  }

  .financial-number {
    @apply font-mono tabular-nums;
  }

  .market-card {
    @apply bg-gradient-to-br from-card via-card to-card/95 border border-border/50 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .bull-gradient {
    @apply bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20;
  }

  .bear-gradient {
    @apply bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-950/20 dark:to-rose-950/20;
  }

  .neutral-gradient {
    @apply bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20;
  }

  .trading-header {
    @apply bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border-b border-primary/20;
  }
}

/* Marquee Animation */
@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 30s linear infinite;
}

.animate-marquee:hover {
  animation-play-state: paused;
}
