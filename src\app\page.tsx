'use client';

import React, { useState, useEffect } from 'react';
import LiveStockListing from '@/components/LiveStockListing';
import LiveStockStatistics from '@/components/LiveStockStatistics';
import TrendingStocks from '@/components/TrendingStocks';
import ThemeToggle from '@/components/ThemeToggle';
import { LiveStock, TrendingStock } from '@/types/stock';
import { fetchLiveStocks, fetchTrendingStocks, testAPI, testTrendingAPI } from '@/services/stockApi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, Loader2 } from 'lucide-react';

export default function Home() {
  const [stocks, setStocks] = useState<LiveStock[]>([]);
  const [trendingStocks, setTrendingStocks] = useState<TrendingStock[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTrendingLoading, setIsTrendingLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [trendingError, setTrendingError] = useState<string | null>(null);

  const loadStocks = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await fetchLiveStocks();
      setStocks(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch stock data');
      console.error('Error loading stocks:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTrendingStocks = async () => {
    try {
      setIsTrendingLoading(true);
      setTrendingError(null);
      const data = await fetchTrendingStocks();
      setTrendingStocks(data);
    } catch (err) {
      setTrendingError(err instanceof Error ? err.message : 'Failed to fetch trending stock data');
      console.error('Error loading trending stocks:', err);
    } finally {
      setIsTrendingLoading(false);
    }
  };

  useEffect(() => {
    loadStocks();
    loadTrendingStocks();

    // Set up auto-refresh every 30 seconds for live data
    const interval = setInterval(() => {
      loadStocks();
      loadTrendingStocks();
    }, 30000);

    // Add test functions to global scope for debugging
    (window as any).testAPI = testAPI;
    (window as any).testTrendingAPI = testTrendingAPI;
    console.log('🔧 Added testAPI() and testTrendingAPI() to window for debugging. Call these functions in console to test the APIs directly.');

    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    loadStocks();
    loadTrendingStocks();
  };

  if (isLoading && stocks.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <ThemeToggle />
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h2 className="text-lg font-semibold mb-2">Loading Live Stock Data</h2>
            <p className="text-muted-foreground text-center">
              Fetching real-time trading information...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error && stocks.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <ThemeToggle />
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">{error}</p>
            <button
              onClick={handleRefresh}
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md transition-colors"
            >
              Try Again
            </button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <ThemeToggle />

      {/* Trending Stocks Marquee */}
      <TrendingStocks stocks={trendingStocks} isLoading={isTrendingLoading} />

      <div className="container mx-auto px-4 py-8">
        <LiveStockStatistics stocks={stocks} />
        <LiveStockListing stocks={stocks} isLoading={isLoading} onRefresh={handleRefresh} />
      </div>
    </div>
  );
}
