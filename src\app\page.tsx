'use client';

import React, { useState, useEffect } from 'react';
import LiveStockListing from '@/components/LiveStockListing';
import LiveStockStatistics from '@/components/LiveStockStatistics';
import ThemeToggle from '@/components/ThemeToggle';
import { LiveStock } from '@/types/stock';
import { fetchLiveStocks, testAPI } from '@/services/stockApi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, Loader2 } from 'lucide-react';

export default function Home() {
  const [stocks, setStocks] = useState<LiveStock[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadStocks = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await fetchLiveStocks();
      setStocks(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch stock data');
      console.error('Error loading stocks:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadStocks();

    // Set up auto-refresh every 30 seconds for live data
    const interval = setInterval(loadStocks, 30000);

    // Add test function to global scope for debugging
    (window as any).testAPI = testAPI;
    console.log('🔧 Added testAPI() to window for debugging. Call testAPI() in console to test the API directly.');

    return () => clearInterval(interval);
  }, []);

  if (isLoading && stocks.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <ThemeToggle />
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h2 className="text-lg font-semibold mb-2">Loading Live Stock Data</h2>
            <p className="text-muted-foreground text-center">
              Fetching real-time trading information...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error && stocks.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <ThemeToggle />
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">{error}</p>
            <button
              onClick={loadStocks}
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md transition-colors"
            >
              Try Again
            </button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <ThemeToggle />
      <div className="container mx-auto px-4 py-8">
        <LiveStockStatistics stocks={stocks} />
        <LiveStockListing stocks={stocks} isLoading={isLoading} onRefresh={loadStocks} />
      </div>
    </div>
  );
}
