'use client';

import React from 'react';
import { ChartDataPoint } from '@/types/stock';

interface SimpleChartProps {
  data: ChartDataPoint[];
  height?: number;
  color?: string;
}

const SimpleChart: React.FC<SimpleChartProps> = ({ 
  data, 
  height = 200, 
  color = '#3b82f6' 
}) => {
  if (!data || data.length === 0) {
    return (
      <div 
        className="flex items-center justify-center bg-gray-50 dark:bg-gray-900 rounded-lg"
        style={{ height }}
      >
        <p className="text-gray-500">No chart data available</p>
      </div>
    );
  }

  // Calculate dimensions and scales
  const width = 800;
  const padding = 40;
  const chartWidth = width - 2 * padding;
  const chartHeight = height - 2 * padding;

  // Find min and max values
  const values = data.map(d => d.value);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);
  const valueRange = maxValue - minValue || 1;

  // Create SVG path
  const createPath = () => {
    if (data.length < 2) return '';

    const points = data.map((point, index) => {
      const x = padding + (index / (data.length - 1)) * chartWidth;
      const y = padding + ((maxValue - point.value) / valueRange) * chartHeight;
      return `${x},${y}`;
    });

    return `M ${points.join(' L ')}`;
  };

  // Create area path (for gradient fill)
  const createAreaPath = () => {
    if (data.length < 2) return '';

    const points = data.map((point, index) => {
      const x = padding + (index / (data.length - 1)) * chartWidth;
      const y = padding + ((maxValue - point.value) / valueRange) * chartHeight;
      return `${x},${y}`;
    });

    const firstPoint = points[0];
    const lastPoint = points[points.length - 1];
    const [lastX] = lastPoint.split(',');
    const [firstX] = firstPoint.split(',');

    return `M ${firstX},${height - padding} L ${points.join(' L ')} L ${lastX},${height - padding} Z`;
  };

  // Format timestamp for tooltip
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // Format value for display
  const formatValue = (value: number) => {
    return `₹${value.toLocaleString()}`;
  };

  return (
    <div className="relative">
      <svg 
        width={width} 
        height={height} 
        className="w-full h-auto border rounded-lg bg-white dark:bg-gray-900"
        viewBox={`0 0 ${width} ${height}`}
      >
        {/* Grid lines */}
        <defs>
          <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e5e7eb" strokeWidth="1" opacity="0.3"/>
          </pattern>
          <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity="0.3"/>
            <stop offset="100%" stopColor={color} stopOpacity="0.05"/>
          </linearGradient>
        </defs>
        
        {/* Grid background */}
        <rect width={width} height={height} fill="url(#grid)" />
        
        {/* Area fill */}
        <path
          d={createAreaPath()}
          fill="url(#areaGradient)"
        />
        
        {/* Main line */}
        <path
          d={createPath()}
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Data points */}
        {data.map((point, index) => {
          const x = padding + (index / (data.length - 1)) * chartWidth;
          const y = padding + ((maxValue - point.value) / valueRange) * chartHeight;
          
          return (
            <g key={index}>
              <circle
                cx={x}
                cy={y}
                r="3"
                fill={color}
                stroke="white"
                strokeWidth="2"
                className="hover:r-5 transition-all cursor-pointer"
              />
              {/* Tooltip on hover */}
              <g className="opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
                <rect
                  x={x - 40}
                  y={y - 35}
                  width="80"
                  height="25"
                  fill="black"
                  fillOpacity="0.8"
                  rx="4"
                />
                <text
                  x={x}
                  y={y - 20}
                  textAnchor="middle"
                  fill="white"
                  fontSize="10"
                  fontWeight="bold"
                >
                  {formatValue(point.value)}
                </text>
                <text
                  x={x}
                  y={y - 10}
                  textAnchor="middle"
                  fill="white"
                  fontSize="8"
                >
                  {formatTime(point.timestamp)}
                </text>
              </g>
            </g>
          );
        })}
        
        {/* Y-axis labels */}
        <text x="10" y={padding} textAnchor="start" fontSize="12" fill="#6b7280">
          {formatValue(maxValue)}
        </text>
        <text x="10" y={height - padding + 5} textAnchor="start" fontSize="12" fill="#6b7280">
          {formatValue(minValue)}
        </text>
        
        {/* X-axis labels */}
        {data.length > 1 && (
          <>
            <text x={padding} y={height - 10} textAnchor="start" fontSize="10" fill="#6b7280">
              {formatTime(data[0].timestamp)}
            </text>
            <text x={width - padding} y={height - 10} textAnchor="end" fontSize="10" fill="#6b7280">
              {formatTime(data[data.length - 1].timestamp)}
            </text>
          </>
        )}
      </svg>
      
      {/* Chart info */}
      <div className="mt-4 flex justify-between text-sm text-gray-600 dark:text-gray-400">
        <div>
          <span className="font-medium">Data Points:</span> {data.length}
        </div>
        <div>
          <span className="font-medium">Range:</span> {formatValue(minValue)} - {formatValue(maxValue)}
        </div>
      </div>
    </div>
  );
};

export default SimpleChart;
