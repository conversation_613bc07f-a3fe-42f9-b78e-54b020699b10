'use client';

import React, { useState, useMemo } from 'react';
import { LiveStock, SortField, SortDirection } from '@/types/stock';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  TrendingUp, 
  Search, 
  Filter, 
  BarChart3, 
  Building2, 
  Factory, 
  Zap, 
  Globe, 
  ChevronUp, 
  ChevronDown, 
  Trash2, 
  ChevronLeft, 
  ChevronRight, 
  FileText,
  ExternalLink,
  TrendingDown,
  Minus,
  RefreshCw
} from 'lucide-react';
import { formatCurrency, formatVolume, formatPercentage, getPriceChangeColor, getPriceChangeIcon } from '@/services/stockApi';

interface LiveStockListingProps {
  stocks: LiveStock[];
  isLoading?: boolean;
  onRefresh?: () => void;
}

const LiveStockListing: React.FC<LiveStockListingProps> = ({ stocks, isLoading = false, onRefresh }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSector, setSelectedSector] = useState<string>('all');
  const [selectedDataSource, setSelectedDataSource] = useState<string>('all');
  const [sortField, setSortField] = useState<SortField>('ticker_name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  // Get unique values for filters
  const uniqueSectors = useMemo(() => {
    const sectors = [...new Set(stocks.map(stock => stock.indices))].sort();
    return sectors;
  }, [stocks]);

  const uniqueDataSources = useMemo(() => {
    const sources = [...new Set(stocks.map(stock => stock.datasource))].sort();
    return sources;
  }, [stocks]);

  // Filter and sort stocks
  const filteredAndSortedStocks = useMemo(() => {
    let filtered = stocks.filter(stock => {
      const matchesSearch = 
        stock.ticker_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stock.ticker.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesSector = selectedSector === 'all' || stock.indices === selectedSector;
      const matchesDataSource = selectedDataSource === 'all' || stock.datasource === selectedDataSource;

      return matchesSearch && matchesSector && matchesDataSource;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [stocks, searchTerm, selectedSector, selectedDataSource, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedStocks.length / itemsPerPage);
  const paginatedStocks = filteredAndSortedStocks.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedSector('all');
    setSelectedDataSource('all');
    setCurrentPage(1);
  };

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />;
    if (change < 0) return <TrendingDown className="h-4 w-4" />;
    return <Minus className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6 mt-6">
      {/* Header */}
      {/* <Card className="market-card">
        <CardHeader className="trading-header">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-green-600 bg-clip-text text-transparent flex items-center gap-3">
                <TrendingUp className="h-8 w-8 text-primary" />
                Live Stock Trading
              </CardTitle>
              <p className="text-muted-foreground text-lg">
                Real-time trading data • {stocks.length} stocks available
              </p>
            </div>
            {onRefresh && (
              <Button 
                onClick={onRefresh} 
                disabled={isLoading}
                variant="outline"
                className="hover:bg-primary/10"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            )}
          </div>
        </CardHeader>
      </Card> */}

      {/* Filters */}
      <Card className="market-card p-6">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5" />
            Advanced Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Search</label>
              <Input
                placeholder="Search by company name, ticker..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Sector</label>
              <Select value={selectedSector} onValueChange={setSelectedSector}>
                <SelectTrigger>
                  <SelectValue placeholder="Select sector" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sectors</SelectItem>
                  {uniqueSectors.map(sector => (
                    <SelectItem key={sector} value={sector}>{sector}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Data Source</label>
              <Select value={selectedDataSource} onValueChange={setSelectedDataSource}>
                <SelectTrigger>
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  {uniqueDataSources.map(source => (
                    <SelectItem key={source} value={source}>{source}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button variant="outline" onClick={clearFilters} className="hover:bg-primary/10 w-full">
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </div>
          
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground financial-number">
              <BarChart3 className="h-4 w-4 inline mr-1" />
              Showing <span className="font-semibold text-primary">{filteredAndSortedStocks.length}</span> of <span className="font-semibold">{stocks.length}</span> stocks
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Results Table */}
      <Card className="market-card ">
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="trading-header">
                <TableHead 
                  className="cursor-pointer hover:bg-primary/10 transition-colors font-semibold"
                  onClick={() => handleSort('ticker_name')}
                >
                  <Building2 className="h-4 w-4 inline mr-2" />
                  Company {sortField === 'ticker_name' && (sortDirection === 'asc' ? <ChevronUp className="h-4 w-4 inline ml-1" /> : <ChevronDown className="h-4 w-4 inline ml-1" />)}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-primary/10 transition-colors font-semibold"
                  onClick={() => handleSort('ticker')}
                >
                  <FileText className="h-4 w-4 inline mr-2" />
                  Ticker {sortField === 'ticker' && (sortDirection === 'asc' ? <ChevronUp className="h-4 w-4 inline ml-1" /> : <ChevronDown className="h-4 w-4 inline ml-1" />)}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-primary/10 transition-colors font-semibold"
                  onClick={() => handleSort('ltp')}
                >
                  <BarChart3 className="h-4 w-4 inline mr-2" />
                  LTP {sortField === 'ltp' && (sortDirection === 'asc' ? <ChevronUp className="h-4 w-4 inline ml-1" /> : <ChevronDown className="h-4 w-4 inline ml-1" />)}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-primary/10 transition-colors font-semibold"
                  onClick={() => handleSort('percentage_change')}
                >
                  <TrendingUp className="h-4 w-4 inline mr-2" />
                  Change {sortField === 'percentage_change' && (sortDirection === 'asc' ? <ChevronUp className="h-4 w-4 inline ml-1" /> : <ChevronDown className="h-4 w-4 inline ml-1" />)}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-primary/10 transition-colors font-semibold"
                  onClick={() => handleSort('volume')}
                >
                  <BarChart3 className="h-4 w-4 inline mr-2" />
                  Volume {sortField === 'volume' && (sortDirection === 'asc' ? <ChevronUp className="h-4 w-4 inline ml-1" /> : <ChevronDown className="h-4 w-4 inline ml-1" />)}
                </TableHead>
                <TableHead className="font-semibold">
                  <Factory className="h-4 w-4 inline mr-2" />
                  Sector
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedStocks.map((stock) => (
                <TableRow 
                  key={stock.ticker} 
                  className="cursor-pointer hover:bg-primary/5 transition-all duration-200 border-b border-border/50"
                >
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                      {stock.icon && (
                        <img 
                          src={`https://${stock.icon}`} 
                          alt={stock.ticker}
                          className="w-8 h-8 rounded-full object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      )}
                      <div>
                        <div className="font-semibold text-foreground">{stock.ticker_name}</div>
                        {/* <div className="text-sm text-muted-foreground">{stock.calculated_on}</div> */}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="stock-symbol text-primary font-bold">{stock.ticker}</TableCell>
                  <TableCell className="financial-number font-bold text-lg">
                    {formatCurrency(stock.ltp)}
                  </TableCell>
                  <TableCell>
                    <div className={`flex items-center gap-1 ${getPriceChangeColor(stock.percentage_change)}`}>
                      {getPriceChangeIcon(stock.percentage_change)}
                      <div className="financial-number">
                        <div className="font-bold">{formatPercentage(stock.percentage_change)}</div>
                        <div className="text-sm">({stock.point_change >= 0 ? '+' : ''}{stock.point_change.toFixed(2)})</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="financial-number font-medium">
                    {formatVolume(stock.volume)}
                  </TableCell>
                  <TableCell className="text-sm">
                    <Badge variant="outline" className="font-medium">
                      {stock.indices}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card className="market-card">
          <CardContent className="flex justify-center items-center space-x-4 py-4">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="hover:bg-primary/10"
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            <span className="text-sm text-muted-foreground financial-number">
              <FileText className="h-4 w-4 inline mr-1" />
              Page <span className="font-semibold text-primary">{currentPage}</span> of <span className="font-semibold">{totalPages}</span>
            </span>
            
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="hover:bg-primary/10"
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LiveStockListing;
