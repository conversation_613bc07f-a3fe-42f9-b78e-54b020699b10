'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  fetchStockAbout, 
  fetchStockQuickView, 
  fetchStockChart, 
  fetchMarketRange, 
  fetchTechnicalIndicators 
} from '@/services/stockApi';
import { 
  StockAbout, 
  StockQuickView, 
  StockChart, 
  MarketRange, 
  TechnicalIndicator, 
  ChartTimeframe 
} from '@/types/stock';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { 
  ArrowLeft, 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Info, 
  Activity,
  Target,
  Zap,
  Loader2
} from 'lucide-react';
import ThemeToggle from '@/components/ThemeToggle';
import SimpleChart from '@/components/SimpleChart';

export default function StockDetailPage() {
  const params = useParams();
  const router = useRouter();
  const ticker = params.ticker as string;

  // State for all data
  const [about, setAbout] = useState<StockAbout | null>(null);
  const [quickView, setQuickView] = useState<StockQuickView | null>(null);
  const [chart, setChart] = useState<StockChart | null>(null);
  const [marketRange, setMarketRange] = useState<MarketRange | null>(null);
  const [technicalIndicators, setTechnicalIndicators] = useState<TechnicalIndicator | null>(null);
  
  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [chartTimeframe, setChartTimeframe] = useState<ChartTimeframe>('1d');
  
  // Error state
  const [error, setError] = useState<string | null>(null);

  // Load all stock data
  const loadStockData = async () => {
    if (!ticker) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      console.log(`🚀 Loading all data for ${ticker}...`);
      
      // Fetch all data in parallel
      const [aboutData, quickViewData, chartData, marketRangeData, technicalData] = await Promise.allSettled([
        fetchStockAbout(ticker),
        fetchStockQuickView(ticker),
        fetchStockChart(ticker, chartTimeframe),
        fetchMarketRange(ticker),
        fetchTechnicalIndicators(ticker)
      ]);

      // Handle results
      if (aboutData.status === 'fulfilled') {
        console.log('✅ About data loaded:', aboutData.value);
        setAbout(aboutData.value);
      } else {
        console.error('❌ Failed to load about data:', aboutData.reason);
      }

      if (quickViewData.status === 'fulfilled') {
        console.log('✅ Quick view data loaded:', quickViewData.value);
        setQuickView(quickViewData.value);
      } else {
        console.error('❌ Failed to load quick view data:', quickViewData.reason);
      }

      if (chartData.status === 'fulfilled') {
        console.log('✅ Chart data loaded:', chartData.value);
        setChart(chartData.value);
      } else {
        console.error('❌ Failed to load chart data:', chartData.reason);
      }

      if (marketRangeData.status === 'fulfilled') {
        console.log('✅ Market range data loaded:', marketRangeData.value);
        setMarketRange(marketRangeData.value);
      } else {
        console.error('Failed to load market range data:', marketRangeData.reason);
      }

      if (technicalData.status === 'fulfilled') {
        setTechnicalIndicators(technicalData.value);
      } else {
        console.error('Failed to load technical indicators:', technicalData.reason);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch stock data');
      console.error('Error loading stock data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Load chart data when timeframe changes
  const loadChartData = async (timeframe: ChartTimeframe) => {
    if (!ticker) return;
    
    try {
      console.log(`📈 Loading chart data for ${ticker} (${timeframe})...`);
      const chartData = await fetchStockChart(ticker, timeframe);
      setChart(chartData);
      setChartTimeframe(timeframe);
    } catch (err) {
      console.error('Error loading chart data:', err);
    }
  };

  useEffect(() => {
    loadStockData();
  }, [ticker]);

  // Helper functions
  const formatCurrency = (amount: number): string => {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return '₹0';
    }
    return `₹${amount.toLocaleString()}`;
  };

  const formatPercentage = (percentage: number): string => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(2)}%`;
  };

  const getPriceChangeColor = (change: number): string => {
    if (change > 0) return 'text-green-600 dark:text-green-400';
    if (change < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />;
    if (change < 0) return <TrendingDown className="h-4 w-4" />;
    return null;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <ThemeToggle />
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h2 className="text-lg font-semibold mb-2">Loading Stock Data</h2>
            <p className="text-muted-foreground text-center">
              Fetching detailed information for {ticker}...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <ThemeToggle />
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <Activity className="h-5 w-5" />
              Error Loading Stock Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">{error}</p>
            <div className="flex gap-2">
              <Button onClick={() => router.back()} variant="outline" className="flex-1">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              <Button onClick={loadStockData} className="flex-1">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <ThemeToggle />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button 
            onClick={() => router.back()} 
            variant="outline" 
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-foreground">{ticker}</h1>
            {quickView && (
              <p className="text-muted-foreground">{quickView.Sector}</p>
            )}
          </div>
          
          {chart && chart.chartData && chart.chartData.length > 0 && (
            <div className="text-right">
              <div className="text-2xl font-bold">
                {formatCurrency(chart.chartData[chart.chartData.length - 1]?.value || 0)}
              </div>
              <div className={`flex items-center gap-1 ${getPriceChangeColor(chart.percentageChange || 0)}`}>
                {getPriceChangeIcon(chart.percentageChange || 0)}
                <span className="font-medium">{formatPercentage(chart.percentageChange || 0)}</span>
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="space-y-6">

          {/* 1. Chart Section */}
          <Card className="market-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-primary" />
                  Price Chart
                </CardTitle>
                <div className="flex gap-2">
                  {(['1m', '3m', '1d', '1y', '5y', 'all'] as ChartTimeframe[]).map((timeframe) => (
                    <Button
                      key={timeframe}
                      variant={chartTimeframe === timeframe ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => loadChartData(timeframe)}
                    >
                      {timeframe.toUpperCase()}
                    </Button>
                  ))}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {chart && chart.chartData && chart.chartData.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold">
                        {formatCurrency(chart.chartData[chart.chartData.length - 1]?.value || 0)}
                      </div>
                      <div className={`flex items-center gap-1 ${getPriceChangeColor(chart.percentageChange || 0)}`}>
                        {getPriceChangeIcon(chart.percentageChange || 0)}
                        <span className="font-medium">{formatPercentage(chart.percentageChange || 0)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Interactive chart */}
                  <SimpleChart
                    data={chart.chartData || []}
                    height={300}
                    color={(chart.percentageChange || 0) >= 0 ? '#10b981' : '#ef4444'}
                  />
                </div>
              ) : (
                <div className="h-64 bg-primary/5 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-2" />
                    <p className="text-muted-foreground">Loading chart data...</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 2. Range Section */}
          {marketRange && (
            <Card className="market-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-primary" />
                  Price Ranges
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">52-Week Range</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">52-Week High</span>
                        <span className="font-semibold text-green-600 financial-number">
                          {formatCurrency(marketRange.high_52w || 0)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">52-Week Low</span>
                        <span className="font-semibold text-red-600 financial-number">
                          {formatCurrency(marketRange.low_52w || 0)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Daily Range</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">Day High</span>
                        <span className="font-semibold text-green-600 financial-number">
                          {formatCurrency(marketRange.high_24h || 0)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">Day Low</span>
                        <span className="font-semibold text-red-600 financial-number">
                          {formatCurrency(marketRange.low_24h || 0)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 3. Overview Section */}
          {about && (
            <Card className="market-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-primary" />
                  About {ticker}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {about.introduction}
                </p>
              </CardContent>
            </Card>
          )}

          {quickView && (
            <Card className="market-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Key Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-primary/5 rounded-lg">
                    <div className="text-2xl font-bold financial-number">{formatCurrency(quickView.open || 0)}</div>
                    <div className="text-sm text-muted-foreground">Open Price</div>
                  </div>
                  <div className="text-center p-4 bg-primary/5 rounded-lg">
                    <div className="text-2xl font-bold financial-number">{(quickView.pe_diluted || 0).toFixed(2)}</div>
                    <div className="text-sm text-muted-foreground">P/E Ratio</div>
                  </div>
                  <div className="text-center p-4 bg-primary/5 rounded-lg">
                    <div className="text-2xl font-bold financial-number">{((quickView.roe || 0) * 100).toFixed(2)}%</div>
                    <div className="text-sm text-muted-foreground">ROE</div>
                  </div>
                  <div className="text-center p-4 bg-primary/5 rounded-lg">
                    <div className="text-2xl font-bold financial-number">{(quickView.beta || 0).toFixed(2)}</div>
                    <div className="text-sm text-muted-foreground">Beta</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 4. Finance Section */}
          {quickView && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="market-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-primary" />
                    Valuation Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">P/E Ratio (Diluted)</span>
                    <span className="font-semibold financial-number">{(quickView.pe_diluted || 0).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">P/B Ratio</span>
                    <span className="font-semibold financial-number">{(quickView.pb_ratio || 0).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">EPS (Diluted)</span>
                    <span className="font-semibold financial-number">{formatCurrency(quickView.eps_diluted || 0)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Book Value</span>
                    <span className="font-semibold financial-number">{formatCurrency(quickView.book_value || 0)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="market-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-primary" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">ROE</span>
                    <span className="font-semibold financial-number">{((quickView.roe || 0) * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Beta</span>
                    <span className="font-semibold financial-number">{(quickView.beta || 0).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Dividend Yield</span>
                    <span className="font-semibold financial-number">{((quickView.div_yield || 0) * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Market Cap</span>
                    <span className="font-semibold financial-number">{formatCurrency(quickView.market_cap || 0)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 5. Technical Section */}
          {technicalIndicators && (
            <Card className="market-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-primary" />
                  Technical Indicators
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">RSI</span>
                      <Badge variant={(technicalIndicators.rsi || 0) > 70 ? 'destructive' : (technicalIndicators.rsi || 0) < 30 ? 'default' : 'secondary'}>
                        {technicalIndicators.rsi?.toFixed(2) || 'N/A'}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">MACD</span>
                      <span className="font-semibold financial-number">
                        {technicalIndicators.macd?.toFixed(2) || 'N/A'}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">MA (20)</span>
                      <span className="font-semibold financial-number">
                        {formatCurrency(technicalIndicators.moving_average_20 || 0)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">MA (50)</span>
                      <span className="font-semibold financial-number">
                        {formatCurrency(technicalIndicators.moving_average_50 || 0)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
