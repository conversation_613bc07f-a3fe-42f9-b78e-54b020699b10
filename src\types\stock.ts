// Legacy stock interface (keeping for reference)
export interface Stock {
  id: number;
  companyName: string;
  symbol: string;
  securityName: string;
  status: string;
  companyEmail: string;
  website: string;
  sectorName: string;
  regulatoryBody: string;
  instrumentType: string;
}

// Live trading data interface
export interface LiveStock {
  ticker: string;
  indices: string;
  ticker_name: string;
  ltp: number; // Last Traded Price
  ltv: number; // Last Traded Volume
  point_change: number;
  percentage_change: number;
  open: number;
  high: number;
  low: number;
  volume: number;
  previousClosing: number;
  calculated_on: string;
  amount: number;
  datasource: string;
  icon: string;
}

// Trending stock interface
export interface TrendingStock {
  ticker: string;
  ticker_name: string;
  latest_price: string;
  points_change: number;
  percentage_change: number;
  traded_of_mkt_cap: number;
}

export type SortField = 'ticker_name' | 'ticker' | 'indices' | 'ltp' | 'percentage_change' | 'volume';
export type SortDirection = 'asc' | 'desc';
