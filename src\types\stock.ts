// Legacy stock interface (keeping for reference)
export interface Stock {
  id: number;
  companyName: string;
  symbol: string;
  securityName: string;
  status: string;
  companyEmail: string;
  website: string;
  sectorName: string;
  regulatoryBody: string;
  instrumentType: string;
}

// Live trading data interface
export interface LiveStock {
  ticker: string;
  indices: string;
  ticker_name: string;
  ltp: number; // Last Traded Price
  ltv: number; // Last Traded Volume
  point_change: number;
  percentage_change: number;
  open: number;
  high: number;
  low: number;
  volume: number;
  previousClosing: number;
  calculated_on: string;
  amount: number;
  datasource: string;
  icon: string;
}

// Trending stock interface
export interface TrendingStock {
  ticker: string;
  ticker_name: string;
  latest_price: string;
  points_change: number;
  percentage_change: number;
  traded_of_mkt_cap: number;
}

// Stock detail page interfaces
export interface StockAbout {
  ticker: string;
  introduction: string;
}

export interface StockQuickView {
  ticker: string;
  open: number;
  eps_diluted: number;
  pe_diluted: number;
  roe: number;
  pb_ratio: number;
  beta: number;
  Sector: string;
  div_yield: number;
  avg_volume: number;
  market_cap: number;
  book_value: number;
  [key: string]: any; // For additional fields
}

export interface ChartDataPoint {
  value: number;
  timestamp: string;
}

export interface StockChart {
  ticker: string;
  percentageChange: number;
  chartData: ChartDataPoint[];
}

export interface MarketRange {
  ticker: string;
  week_52_high: number;
  week_52_low: number;
  day_high: number;
  day_low: number;
  [key: string]: any;
}

export interface TechnicalIndicator {
  ticker: string;
  rsi: number;
  macd: number;
  moving_average_20: number;
  moving_average_50: number;
  [key: string]: any;
}

// NEPSE Index data interface
export interface NepseIndex {
  indices_name: string;
  point_change: number;
  percentage_change: number;
  calculated_on: string;
  latest_price: number;
  chartData: ChartDataPoint[];
}

export type ChartTimeframe = '1m' | '3m' | '1d' | '1y' | '5y' | 'all';
export type NepseTimeframe = '1m' | '3m' | '1d' | '1y';

export type SortField = 'ticker_name' | 'ticker' | 'indices' | 'ltp' | 'percentage_change' | 'volume';
export type SortDirection = 'asc' | 'desc';
