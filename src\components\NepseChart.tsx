'use client';

import React, { useState, useEffect } from 'react';
import { fetchNepseIndex } from '@/services/stockApi';
import { NepseIndex, NepseTimeframe } from '@/types/stock';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Activity,
  Loader2,
  RefreshCw
} from 'lucide-react';
import SimpleChart from '@/components/SimpleChart';

interface NepseChartProps {
  className?: string;
}

const NepseChart: React.FC<NepseChartProps> = ({ className = '' }) => {
  const [nepseData, setNepseData] = useState<NepseIndex | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState<NepseTimeframe>('1d');

  // Load NEPSE data
  const loadNepseData = async (selectedTimeframe: NepseTimeframe = timeframe) => {
    try {
      setIsLoading(true);
      setError(null);
      console.log(`🚀 Loading NEPSE data for ${selectedTimeframe}...`);
      
      const data = await fetchNepseIndex(selectedTimeframe);
      setNepseData(data);
      setTimeframe(selectedTimeframe);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch NEPSE data');
      console.error('Error loading NEPSE data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadNepseData();
    
    // Auto-refresh every 2 minutes for live data
    const interval = setInterval(() => loadNepseData(), 120000);
    
    return () => clearInterval(interval);
  }, []);

  // Helper functions
  const formatPrice = (price: number): string => {
    return price.toLocaleString('en-NP', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    });
  };

  const formatPercentage = (percentage: number): string => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(2)}%`;
  };

  const formatPoints = (points: number): string => {
    const sign = points >= 0 ? '+' : '';
    return `${sign}${points.toFixed(2)}`;
  };

  const getPriceChangeColor = (change: number): string => {
    if (change > 0) return 'text-green-600 dark:text-green-400';
    if (change < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />;
    if (change < 0) return <TrendingDown className="h-4 w-4" />;
    return null;
  };

  const getBadgeVariant = (change: number) => {
    if (change > 0) return 'default';
    if (change < 0) return 'destructive';
    return 'secondary';
  };

  if (error) {
    return (
      <Card className={`market-card ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <Activity className="h-5 w-5" />
            NEPSE Index - Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => loadNepseData()} size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`market-card ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            NEPSE Index
          </CardTitle>
          
          {/* Timeframe buttons */}
          <div className="flex gap-2">
            {(['1m', '3m', '1d', '1y'] as NepseTimeframe[]).map((tf) => (
              <Button
                key={tf}
                variant={timeframe === tf ? 'default' : 'outline'}
                size="sm"
                onClick={() => loadNepseData(tf)}
                disabled={isLoading}
              >
                {tf.toUpperCase()}
              </Button>
            ))}
          </div>
        </div>
        
        {/* Current price and change */}
        {nepseData && !isLoading && (
          <div className="flex items-center justify-between mt-4">
            <div>
              <div className="text-3xl font-bold financial-number">
                {formatPrice(nepseData.latest_price)}
              </div>
              <div className="text-sm text-muted-foreground">
                Updated: {new Date(nepseData.calculated_on).toLocaleString()}
              </div>
            </div>
            
            <div className="text-right">
              <div className="flex items-center gap-2 justify-end mb-1">
                <Badge 
                  variant={getBadgeVariant(nepseData.percentage_change)}
                  className="flex items-center gap-1"
                >
                  {getPriceChangeIcon(nepseData.percentage_change)}
                  {formatPercentage(nepseData.percentage_change)}
                </Badge>
              </div>
              <div className={`text-sm ${getPriceChangeColor(nepseData.point_change)}`}>
                {formatPoints(nepseData.point_change)} points
              </div>
            </div>
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-2" />
              <p className="text-muted-foreground">Loading NEPSE data...</p>
            </div>
          </div>
        ) : nepseData && nepseData.chartData && nepseData.chartData.length > 0 ? (
          <div className="space-y-4">
            {/* Chart */}
            <SimpleChart 
              data={nepseData.chartData} 
              height={300}
              color={nepseData.percentage_change >= 0 ? '#10b981' : '#ef4444'}
            />
            
            {/* Chart info */}
            <div className="flex justify-between text-sm text-muted-foreground">
              <div>
                <span className="font-medium">Timeframe:</span> {timeframe.toUpperCase()}
              </div>
              <div>
                <span className="font-medium">Data Points:</span> {nepseData.chartData.length}
              </div>
            </div>
          </div>
        ) : (
          <div className="h-64 bg-primary/5 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-primary/50 mx-auto mb-2" />
              <p className="text-muted-foreground">No chart data available</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default NepseChart;
