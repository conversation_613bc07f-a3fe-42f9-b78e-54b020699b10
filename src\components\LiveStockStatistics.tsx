'use client';

import React, { useMemo } from 'react';
import { LiveStock } from '@/types/stock';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  DollarSign, 
  Activity, 
  Building2,
  Trophy,
  Minus
} from 'lucide-react';
import { formatCurrency, formatVolume } from '@/services/stockApi';

interface LiveStockStatisticsProps {
  stocks: LiveStock[];
}

const LiveStockStatistics: React.FC<LiveStockStatisticsProps> = ({ stocks }) => {
  const statistics = useMemo(() => {
    const totalStocks = stocks.length;
    
    // Price movement analysis
    const gainers = stocks.filter(stock => stock.percentage_change > 0);
    const losers = stocks.filter(stock => stock.percentage_change < 0);
    const unchanged = stocks.filter(stock => stock.percentage_change === 0);

    // Sector distribution
    const sectorCounts = stocks.reduce((acc, stock) => {
      acc[stock.indices] = (acc[stock.indices] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Top gainers and losers
    const topGainers = [...stocks]
      .filter(stock => stock.percentage_change > 0)
      .sort((a, b) => b.percentage_change - a.percentage_change)
      .slice(0, 5);

    const topLosers = [...stocks]
      .filter(stock => stock.percentage_change < 0)
      .sort((a, b) => a.percentage_change - b.percentage_change)
      .slice(0, 5);

    // Volume analysis
    const totalVolume = stocks.reduce((sum, stock) => sum + stock.volume, 0);
    const totalAmount = stocks.reduce((sum, stock) => sum + stock.amount, 0);

    // Top sectors by count
    const topSectors = Object.entries(sectorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);

    // Market sentiment
    const bullishPercentage = ((gainers.length / totalStocks) * 100);
    const bearishPercentage = ((losers.length / totalStocks) * 100);

    return {
      totalStocks,
      gainers: gainers.length,
      losers: losers.length,
      unchanged: unchanged.length,
      topGainers,
      topLosers,
      totalVolume,
      totalAmount,
      topSectors,
      bullishPercentage,
      bearishPercentage
    };
  }, [stocks]);

  return (
    <div className="space-y-6">
      {/* Market Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Stocks */}
        <Card className="market-card bull-gradient">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">Total Stocks</CardTitle>
            <BarChart3 className="h-5 w-5 text-green-600 dark:text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold financial-number text-green-600 dark:text-green-400">
              {statistics.totalStocks}
            </div>
            <p className="text-xs text-green-600/70 dark:text-green-400/70 font-medium">
              Active trading stocks
            </p>
          </CardContent>
        </Card>

        {/* Market Sentiment */}
        <Card className="market-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Market Sentiment</CardTitle>
            <Activity className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Gainers</span>
                </div>
                <span className="text-sm font-bold text-green-600">{statistics.gainers}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <TrendingDown className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium">Losers</span>
                </div>
                <span className="text-sm font-bold text-red-600">{statistics.losers}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <Minus className="h-4 w-4 text-gray-600" />
                  <span className="text-sm font-medium">Unchanged</span>
                </div>
                <span className="text-sm font-bold text-gray-600">{statistics.unchanged}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Volume */}
        <Card className="market-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Total Volume</CardTitle>
            <BarChart3 className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold financial-number text-purple-600 dark:text-purple-400">
              {formatVolume(statistics.totalVolume)}
            </div>
            <p className="text-xs text-purple-600/70 dark:text-purple-400/70 font-medium">
              Shares traded
            </p>
          </CardContent>
        </Card>

        {/* Total Amount */}
        <Card className="market-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Total Amount</CardTitle>
            <DollarSign className="h-5 w-5 text-orange-600 dark:text-orange-400" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold financial-number text-orange-600 dark:text-orange-400">
              {formatCurrency(statistics.totalAmount)}
            </div>
            <p className="text-xs text-orange-600/70 dark:text-orange-400/70 font-medium">
              Trading value
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Gainers */}
        <Card className="market-card bull-gradient">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2 text-green-700 dark:text-green-300">
              <TrendingUp className="h-5 w-5" />
              Top Gainers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {statistics.topGainers.map((stock, index) => (
                <div key={stock.ticker} className="flex items-center justify-between p-2 rounded-lg bg-green-50 dark:bg-green-950/20">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs font-bold">#{index + 1}</Badge>
                    <div>
                      <div className="font-semibold text-sm">{stock.ticker}</div>
                      <div className="text-xs text-muted-foreground">{stock.ticker_name}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-green-600 dark:text-green-400">
                      +{stock.percentage_change.toFixed(2)}%
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatCurrency(stock.ltp)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Losers */}
        <Card className="market-card bear-gradient">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2 text-red-700 dark:text-red-300">
              <TrendingDown className="h-5 w-5" />
              Top Losers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {statistics.topLosers.map((stock, index) => (
                <div key={stock.ticker} className="flex items-center justify-between p-2 rounded-lg bg-red-50 dark:bg-red-950/20">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs font-bold">#{index + 1}</Badge>
                    <div>
                      <div className="font-semibold text-sm">{stock.ticker}</div>
                      <div className="text-xs text-muted-foreground">{stock.ticker_name}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-red-600 dark:text-red-400">
                      {stock.percentage_change.toFixed(2)}%
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatCurrency(stock.ltp)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Sectors */}
      <Card className="market-card">
        <CardHeader className="trading-header">
          <CardTitle className="text-xl font-bold flex items-center gap-2">
            <Trophy className="h-6 w-6" />
            Top Sectors by Stock Count
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {statistics.topSectors.map(([sector, count], index) => (
              <div key={sector} className="text-center p-4 border rounded-lg market-card hover:shadow-md transition-all duration-200">
                <div className="text-3xl font-bold text-primary mb-2">#{index + 1}</div>
                <div className="text-sm font-semibold mb-2 text-foreground">{sector}</div>
                <div className="text-2xl font-bold financial-number text-primary">{count}</div>
                <div className="text-xs text-muted-foreground font-medium">stocks</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LiveStockStatistics;
